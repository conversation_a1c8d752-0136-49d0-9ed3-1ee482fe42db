# Graphviz 解析器优化总结

## 问题描述

原有的 Graphviz 解析器存在以下问题：

1. **subgraph 没有表示**：解析器无法处理 `subgraph` 和 `cluster` 结构
2. **label 属性无法正常渲染**：HTML 格式的 label 无法正确解析和显示

## 解决方案

### 1. 添加 Subgraph 支持

#### 新增数据模型
- **GraphSubgraphData**: 表示 subgraph/cluster 的数据结构
  - `name`: subgraph 名称
  - `label`: 显示标签
  - `nodes`: 包含的节点列表
  - `edges`: 包含的边列表
  - `attributes`: 属性映射
  - `isCluster`: 是否为 cluster 类型

#### 更新现有模型
- **GraphDiagramData**: 添加 `subgraphs` 字段
- **CodeTopologyDataModel**: 支持 subgraph 渲染

#### 解析逻辑
- 在 `DotFileParser` 中添加 `extractSubgraphs()` 方法
- 递归解析嵌套的 subgraph 结构
- 正确识别 `cluster_` 前缀的 cluster 类型

### 2. HTML Label 解析支持

#### 新增 HTML 解析器
- **HtmlLabelParser**: 专门处理 HTML 格式的 label
  - `parseHtmlLabel()`: 将 HTML 转换为纯文本
  - `isHtmlLabel()`: 检测是否为 HTML label
  - `parseStructuredHtml()`: 提取结构化信息

#### 支持的 HTML 标签
- `<b>...</b>`: 粗体文本 → `**...**`
- `<i>...</i>`: 斜体文本 → `*...*`
- `<u>...</u>`: 下划线文本 → `_..._`
- `<br/>`: 换行符 → `\n`
- 其他标签：移除标签但保留内容

#### 结构化信息提取
- **HtmlLabelInfo**: 包含标题、描述、纯文本等信息
  - `title`: 第一个 `<b>` 标签的内容
  - `description`: 第一个 `<br/>` 后的内容
  - `plainText`: 完整的纯文本版本

### 3. 渲染优化

#### Tooltip 增强
- 在 `CodeTopologyElementManager` 中区分不同节点类型
- 为 cluster 节点显示特殊的 tooltip 信息
- 显示背景色等 cluster 特有属性

#### 节点类型支持
- 扩展 `GraphvizNodeType` 枚举
- 添加 `CLUSTER` 类型支持
- 在渲染时正确处理不同类型的节点

## 测试验证

### 测试用例
创建了全面的测试用例验证新功能：

1. **Subgraph 解析测试**
   - 基本 subgraph 解析
   - Cluster 识别
   - 嵌套 subgraph 处理
   - 节点和边的正确归属

2. **HTML Label 解析测试**
   - HTML 标签检测
   - 标签转换
   - 结构化信息提取
   - 复杂 HTML 处理

3. **集成测试**
   - 完整的旅行代理示例解析
   - 多个 cluster 的处理
   - HTML label 和 subgraph 的组合使用

### 示例验证

使用提供的旅行代理示例进行验证：

```dot
digraph G {
    rankdir=TB;
    node [shape=box, style=rounded];

    subgraph cluster_agents {
        label="代理定义";
        TravelAgent [label=<
            <b>旅行代理</b><br/>协调预订航班、酒店和支付
        >];
        // ... 其他节点
    }

    subgraph cluster_parallel {
        label="1. 并行查询";
        TravelAgent -> FlightAgent [label="findFlights()"];
        TravelAgent -> HotelAgent [label="findHotels()"];
    }
    
    // ... 其他 subgraph
}
```

**解析结果**：
- ✅ 正确识别 3 个 subgraph/cluster
- ✅ 正确解析 HTML label 中的粗体和换行
- ✅ 正确归属节点和边到相应的 subgraph
- ✅ 保持原有功能的兼容性

## 兼容性

### 向后兼容
- 所有现有的 DOT 文件解析功能保持不变
- 新增的 `subgraphs` 字段默认为空列表
- 非 HTML label 的处理逻辑不变

### 错误处理
- 解析失败时优雅降级
- 无效 HTML 标签会被安全移除
- Subgraph 解析错误不影响主图解析

## 性能影响

- HTML label 解析仅在检测到 HTML 格式时触发
- Subgraph 解析复杂度为 O(n)，其中 n 为 subgraph 数量
- 内存使用增加微乎其微（仅存储 subgraph 元数据）

## 总结

通过这次优化，Graphviz 解析器现在能够：

1. **完整支持 subgraph/cluster 结构**，包括嵌套和属性
2. **正确解析和渲染 HTML label**，提供更丰富的文本格式
3. **保持完全的向后兼容性**，不影响现有功能
4. **提供全面的测试覆盖**，确保功能稳定性

这些改进显著增强了 Graphviz 图表的表达能力和用户体验。
