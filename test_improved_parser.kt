import cc.unitmesh.diagram.parser.DotFileParser
import cc.unitmesh.diagram.parser.HtmlLabelParser

fun main() {
    val dotContent = """
digraph G {
    rankdir=TB;
    node [shape=box, style=rounded];

    subgraph cluster_agents {
        label="代理定义";
        TravelAgent [label=<
            <b>旅行代理</b><br/>协调预订航班、酒店和支付
        >];
        FlightAgent [label=<
            <b>航班代理</b><br/>技能: findFlights(query: object): flightOptions
        >];
        HotelAgent [label=<
            <b>酒店代理</b><br/>技能: findHotels(query: object): hotelOptions
        >];
        PaymentAgent [label=<
            <b>支付代理</b><br/>技能: processPayment(amount: float): transactionID
        >];
        RefundHandler [label=<
            <b>退款处理代理</b><br/>技能: initiateRefund(transactionID: string): void
        >];
    }

    subgraph cluster_parallel {
        label="1. 并行查询";
        TravelAgent -> FlightAgent [label="findFlights()"];
        TravelAgent -> HotelAgent [label="findHotels()"];
    }

    subgraph cluster_conditional {
        label="2. 条件决策与支付";
        TravelAgent -> PaymentAgent [label="processPayment()"];
        TravelAgent -> User [label="无法满足所有预订要求"];
    }

    PaymentAgent -> RefundHandler [label="initiateRefund()", color=red, style=dashed];
}
    """.trimIndent()

    println("=== Testing HTML Label Parser ===")
    val htmlLabel = """<b>旅行代理</b><br/>协调预订航班、酒店和支付"""
    println("Original HTML: $htmlLabel")
    println("Is HTML Label: ${HtmlLabelParser.isHtmlLabel(htmlLabel)}")
    println("Parsed Text: ${HtmlLabelParser.parseHtmlLabel(htmlLabel)}")
    
    val structuredInfo = HtmlLabelParser.parseStructuredHtml(htmlLabel)
    println("Structured Info:")
    println("  Title: ${structuredInfo.title}")
    println("  Description: ${structuredInfo.description}")
    println("  Plain Text: ${structuredInfo.plainText}")

    println("\n=== Testing Improved DotFileParser ===")
    try {
        val parser = DotFileParser()
        val result = parser.parse(dotContent)
        
        println("Parsed result: $result")
        println("Nodes: ${result.nodes.size}")
        println("Entities: ${result.entities.size}")
        println("Edges: ${result.edges.size}")
        println("Subgraphs: ${result.subgraphs.size}")
        
        println("\n--- Nodes ---")
        result.nodes.forEach { node ->
            println("Node: ${node.getName()}")
            println("  Label: ${node.getDisplayLabel()}")
            println("  Type: ${node.getNodeType()}")
            println("  Attributes: ${node.getAttributes()}")
        }
        
        println("\n--- Subgraphs ---")
        result.subgraphs.forEach { subgraph ->
            println("Subgraph: ${subgraph.name}")
            println("  Label: ${subgraph.getDisplayLabel()}")
            println("  Is Cluster: ${subgraph.isCluster}")
            println("  Nodes: ${subgraph.nodes}")
            println("  Edges: ${subgraph.edges.size}")
            println("  Attributes: ${subgraph.attributes}")
        }
        
        println("\n--- Edges ---")
        result.edges.forEach { edge ->
            println("Edge: ${edge.sourceNodeId} -> ${edge.targetNodeId}")
            println("  Label: ${edge.label}")
            println("  Attributes: ${edge.attributes}")
        }
        
    } catch (e: Exception) {
        println("Error parsing with improved parser: ${e.message}")
        e.printStackTrace()
    }
}
