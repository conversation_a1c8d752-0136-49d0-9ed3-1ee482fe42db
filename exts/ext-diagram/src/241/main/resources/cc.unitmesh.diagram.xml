<idea-plugin package="cc.unitmesh.diagram">
    <!--suppress PluginXmlValidity -->
    <dependencies>
        <plugin id="com.intellij.diagram"/>
    </dependencies>

    <extensions defaultExtensionNs="com.intellij">
        <!-- Register file types -->
        <fileType name="DOT" implementationClass="cc.unitmesh.diagram.idea.DotFileType"
                  fieldName="INSTANCE" extensions="dot;gv;graphviz"/>
        <fileType name="Mermaid" implementationClass="cc.unitmesh.diagram.idea.MermaidFileType"
                  fieldName="INSTANCE" extensions="mmd;mermaid"/>

        <!-- Register Graphviz diagram provider -->
        <diagram.Provider implementation="cc.unitmesh.diagram.sketch.CodeTopologyUmlProvider"/>

        <!-- Register project service -->
        <projectService serviceImplementation="cc.unitmesh.diagram.diagram.CodeTopologyDiagramService"/>

        <!-- Register file editor providers -->
        <fileEditorProvider implementation="cc.unitmesh.diagram.editor.GraphvizSplitEditorProvider"/>
        <fileEditorProvider implementation="cc.unitmesh.diagram.editor.mermaid.MermaidSplitEditorProvider"/>
    </extensions>

    <extensions defaultExtensionNs="cc.unitmesh">
        <!-- Register Graphviz language sketch provider -->
        <langSketchProvider implementation="cc.unitmesh.diagram.diagram.CodeTopologySketchProvider"/>
    </extensions>
</idea-plugin>
