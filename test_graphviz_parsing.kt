import cc.unitmesh.diagram.parser.DotFileParser
import guru.nidi.graphviz.parse.Parser

fun main() {
    val dotContent = """
digraph G {
    rankdir=TB;
    node [shape=box, style=rounded];

    subgraph cluster_agents {
        label="代理定义";
        TravelAgent [label=<
            <b>旅行代理</b><br/>协调预订航班、酒店和支付
        >];
        FlightAgent [label=<
            <b>航班代理</b><br/>技能: findFlights(query: object): flightOptions
        >];
        HotelAgent [label=<
            <b>酒店代理</b><br/>技能: findHotels(query: object): hotelOptions
        >];
        PaymentAgent [label=<
            <b>支付代理</b><br/>技能: processPayment(amount: float): transactionID
        >];
        RefundHandler [label=<
            <b>退款处理代理</b><br/>技能: initiateRefund(transactionID: string): void
        >];
    }

    subgraph cluster_parallel {
        label="1. 并行查询";
        TravelAgent -> FlightAgent [label="findFlights()"];
        TravelAgent -> HotelAgent [label="findHotels()"];
    }

    subgraph cluster_conditional {
        label="2. 条件决策与支付";
        TravelAgent -> PaymentAgent [label="processPayment()"];
        TravelAgent -> User [label="无法满足所有预订要求"];
    }

    PaymentAgent -> RefundHandler [label="initiateRefund()", color=red, style=dashed];
}
    """.trimIndent()

    // Test guru.nidi.graphviz parsing
    println("=== Testing guru.nidi.graphviz parsing ===")
    try {
        val graph = Parser().read(dotContent)
        println("Graph type: ${if (graph.isDirected) "DIGRAPH" else "GRAPH"}")
        println("Graph attributes: ${graph.graphAttrs()}")
        
        // Check for subgraphs
        println("\n=== Subgraphs ===")
        val subgraphs = graph.graphs()
        println("Number of subgraphs: ${subgraphs.size}")
        subgraphs.forEach { subgraph ->
            println("Subgraph: ${subgraph.name()}")
            println("  Attributes: ${subgraph.graphAttrs()}")
            println("  Nodes: ${subgraph.nodes().map { it.name() }}")
        }
        
        println("\n=== Nodes ===")
        graph.nodes().forEach { node ->
            println("Node: ${node.name()}")
            println("  Attributes: ${node.attrs()}")
            val label = node.attrs().find { it.key == "label" }?.value
            println("  Label: $label")
        }
        
        println("\n=== Edges ===")
        graph.edges().forEach { edge ->
            println("Edge: ${edge.from()?.name()} -> ${edge.to()?.name()}")
            println("  Attributes: ${edge.attrs()}")
        }
        
    } catch (e: Exception) {
        println("Error parsing with guru.nidi.graphviz: ${e.message}")
        e.printStackTrace()
    }

    // Test our parser
    println("\n\n=== Testing our DotFileParser ===")
    try {
        val parser = DotFileParser()
        val result = parser.parse(dotContent)
        println("Parsed result: $result")
        println("Nodes: ${result.nodes.size}")
        println("Entities: ${result.entities.size}")
        println("Edges: ${result.edges.size}")
        
        result.nodes.forEach { node ->
            println("Node: ${node.getName()}, Label: ${node.getDisplayLabel()}")
        }
        
        result.edges.forEach { edge ->
            println("Edge: ${edge.sourceNodeId} -> ${edge.targetNodeId}, Label: ${edge.label}")
        }
        
    } catch (e: Exception) {
        println("Error parsing with our parser: ${e.message}")
        e.printStackTrace()
    }
}
