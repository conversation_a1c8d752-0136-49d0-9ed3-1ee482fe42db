import cc.unitmesh.diagram.parser.DotFileParser
import cc.unitmesh.diagram.parser.HtmlLabelParser

fun main() {
    val dotContent = """
digraph G {
    rankdir=TB;
    node [shape=box, style=rounded];

    subgraph cluster_agents {
        label="代理定义";
        TravelAgent [label=<
            <b>旅行代理</b><br/>协调预订航班、酒店和支付
        >];
        FlightAgent [label=<
            <b>航班代理</b><br/>技能: findFlights(query: object): flightOptions
        >];
    }

    subgraph cluster_parallel {
        label="1. 并行查询";
        TravelAgent -> FlightAgent [label="findFlights()"];
    }

    PaymentAgent -> TravelAgent [label="processPayment()"];
}
    """.trimIndent()

    println("=== 测试 HTML Label 解析 ===")
    val htmlLabel = """<b>旅行代理</b><br/>协调预订航班、酒店和支付"""
    println("原始 HTML: $htmlLabel")
    println("是否为 HTML: ${HtmlLabelParser.isHtmlLabel(htmlLabel)}")
    println("解析结果: ${HtmlLabelParser.parseHtmlLabel(htmlLabel)}")
    
    val structuredInfo = HtmlLabelParser.parseStructuredHtml(htmlLabel)
    println("结构化信息:")
    println("  标题: ${structuredInfo.title}")
    println("  描述: ${structuredInfo.description}")
    println("  纯文本: ${structuredInfo.plainText}")

    println("\n=== 测试 DotFileParser ===")
    try {
        val parser = DotFileParser()
        val result = parser.parse(dotContent)
        
        println("解析结果: $result")
        println("节点数量: ${result.nodes.size}")
        println("实体数量: ${result.entities.size}")
        println("边数量: ${result.edges.size}")
        println("子图数量: ${result.subgraphs.size}")
        
        println("\n--- 节点 ---")
        result.nodes.forEach { node ->
            println("节点: ${node.getName()}")
            println("  标签: ${node.getDisplayLabel()}")
            println("  类型: ${node.getNodeType()}")
        }
        
        println("\n--- 子图 ---")
        result.subgraphs.forEach { subgraph ->
            println("子图: ${subgraph.name}")
            println("  标签: ${subgraph.getDisplayLabel()}")
            println("  是否为 Cluster: ${subgraph.isCluster}")
            println("  包含节点: ${subgraph.nodes}")
            println("  边数量: ${subgraph.edges.size}")
        }
        
        println("\n--- 边 ---")
        result.edges.forEach { edge ->
            println("边: ${edge.sourceNodeId} -> ${edge.targetNodeId}")
            println("  标签: ${edge.label}")
        }
        
    } catch (e: Exception) {
        println("解析错误: ${e.message}")
        e.printStackTrace()
    }
}
